import org.junit.jupiter.api.Test;
import org.web3j.abi.EventEncoder;
import org.web3j.abi.TypeReference;
import org.web3j.abi.datatypes.Address;
import org.web3j.abi.datatypes.Event;
import org.web3j.abi.datatypes.generated.Uint256;
import org.web3j.crypto.Hash;

import java.util.Arrays;

public class SignatureDebugTest {

    @Test
    public void testTransferEventSignature() {
        // Create Transfer event: Transfer(address indexed from, address indexed to, uint256 value)
        TypeReference<Address> fromRef = new TypeReference<Address>(true) {};
        TypeReference<Address> toRef = new TypeReference<Address>(true) {};
        TypeReference<Uint256> valueRef = new TypeReference<Uint256>(false) {};
        
        Event transferEvent = new Event("Transfer", 
            Arrays.asList(fromRef, toRef, valueRef));
        
        // Get signature from EventEncoder
        String eventEncoderSignature = EventEncoder.encode(transferEvent);
        
        // Create manual signature
        String manualSignature = Hash.sha3String("Transfer(address,address,uint256)");
        
        // Expected Transfer signature (well-known)
        String expectedTransferSignature = "0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef";

        // Print for debugging
        System.out.println("EventEncoder signature: " + eventEncoderSignature);
        System.out.println("Manual signature: " + manualSignature);
        System.out.println("Expected Transfer signature: " + expectedTransferSignature);

        // Assertions
        assert eventEncoderSignature.equals(manualSignature) : "EventEncoder and manual signatures should match";
        assert eventEncoderSignature.equals(expectedTransferSignature) : "EventEncoder should match expected Transfer signature";
    }
}
