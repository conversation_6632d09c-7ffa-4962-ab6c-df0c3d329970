package com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3;

import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties;
import com.decurret_dcp.dcjpy.bcmonitoring.consts.DCFConst;
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.ContractEvents;
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.ContractInfo;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.web3j.abi.EventEncoder;
import org.web3j.abi.TypeReference;
import org.web3j.abi.datatypes.*;
import org.web3j.abi.datatypes.generated.Bytes32;
import org.web3j.abi.datatypes.generated.Uint16;
import org.web3j.abi.datatypes.generated.Uint256;

import org.web3j.protocol.core.methods.response.AbiDefinition;
import org.web3j.protocol.core.methods.response.Log;

@Component
public class AbiParser {
  private static final Logger log = LoggerFactory.getLogger(AbiParser.class);
  private final ObjectMapper mapper = new ObjectMapper();
  public static Map<String, ContractEvents> contractEventStore = new HashMap<>();
  public static List<String> contractAddresses = new ArrayList<>();

  private final BcmonitoringConfigurationProperties properties;

  /** ABI event input parameter that stores name, type reference and indexing information */
  public static class AbiEventInput {
    private final String name;
    private final String type;
    private final boolean indexed;
    private final List<AbiEventInput> components;

    public AbiEventInput(String name, String type, boolean indexed) {
      this.name = name;
      this.type = type;
      this.indexed = indexed;
      this.components = null;
    }

    public AbiEventInput(
        String name, String type, boolean indexed, List<AbiEventInput> components) {
      this.name = name;
      this.type = type;
      this.indexed = indexed;
      this.components = components != null ? List.copyOf(components) : null;
    }

    public String getName() {
      return name;
    }

    public String getType() {
      return type;
    }

    public boolean isIndexed() {
      return indexed;
    }

    public List<AbiEventInput> getComponents() {
      return components;
    }

    public boolean isTuple() {
      return type != null && type.startsWith("tuple");
    }
  }

  /** Contract ABI event definition with parameter metadata */
  public static class ContractAbiEvent {
    private final Event event;
    private final List<AbiEventInput> inputs;

    public ContractAbiEvent(Event event, List<AbiEventInput> inputs) {
      this.event = event;
      this.inputs = List.copyOf(inputs);
    }

    public Event getEvent() {
      return event;
    }

    public List<AbiEventInput> getInputs() {
      return inputs;
    }
  }

  public AbiParser(BcmonitoringConfigurationProperties properties) {
    this.properties = properties;
  }

  /**
   * Parse ABI content from JSON string
   *
   * @param abiContent ABI content in JSON format
   * @throws IOException If parsing fails
   */
  public Map<String, ContractAbiEvent> parseAbi(String abiContent) throws IOException {
    Map<String, ContractAbiEvent> stringEventMap = new HashMap<>();
    if (abiContent == null || abiContent.isEmpty()) {
      log.warn("Empty ABI content provided");
      return stringEventMap;
    }

    try {
      // Parse ABI content as array of AbiDefinition objects
      AbiDefinition[] abiDefinitions = mapper.readValue(abiContent, AbiDefinition[].class);

      for (AbiDefinition abiDefinition : abiDefinitions) {
        if ("event".equals(abiDefinition.getType())) {
          String eventName = abiDefinition.getName();
          if (eventName == null || eventName.isEmpty()) {
            // Skip events with missing or empty names
            continue;
          }

          List<TypeReference<?>> typeReferences = new ArrayList<>();
          List<AbiEventInput> eventInputs = new ArrayList<>();

          // Parse inputs from AbiDefinition
          List<AbiDefinition.NamedType> inputs = abiDefinition.getInputs();
          if (inputs != null) {
            for (AbiDefinition.NamedType input : inputs) {

              TypeReference<?> typeReference = createTypeReference(input);
              typeReferences.add(typeReference);

              // Create AbiEventInput with tuple component processing
              AbiEventInput eventInput = createAbiEventInput(input);
              eventInputs.add(eventInput);
            }

            // Create event (Web3j expects 2 arguments)
            Event event = new Event(eventName, typeReferences);
            ContractAbiEvent contractAbiEvent = new ContractAbiEvent(event, eventInputs);

            // Create signature for the event
            String signature = EventEncoder.encode(event);
            log.debug(
                "Event: {}, Parameters: {}, Signature: {}", eventName, eventInputs, signature);

            // Store contract ABI event in contractEventStore
            stringEventMap.put(signature, contractAbiEvent);

            log.debug("Parsed event: {} with signature: {}", eventName, signature);
          }
        }
      }

      log.info("Successfully parsed ABI with {} events", stringEventMap.size());
    } catch (Exception e) {
      log.error("Failed to parse ABI content", e);
      throw new IOException("ABI parsing failed: " + e.getMessage(), e);
    }
    return stringEventMap;
  }

  /**
   * Generic DynamicStruct that can be created from any list of components
   * This replaces the hardcoded InputDynamicStruct to support any tuple structure from ABI
   */
  public static class GenericDynamicStruct extends DynamicStruct {
    @SuppressWarnings("rawtypes")
    public GenericDynamicStruct(List<Type> components) {
      super(components);
    }
  }



  /** Create appropriate TypeReference based on the Solidity type */
  private TypeReference<?> createTypeReference(AbiDefinition.NamedType input) {

    boolean indexed = input.isIndexed();
    String solidityType = input.getType();

    // Handle tuple types - create TypeReference with innerTypes for EventEncoder compatibility
    if (solidityType.startsWith("tuple")) {
      boolean isArray = solidityType.endsWith("[]");
      if (isArray) { // tuple[]
        return new TypeReference<DynamicArray<GenericDynamicStruct>>(indexed) {};
      } else { // tuple
        return new TypeReference<GenericDynamicStruct>(indexed) {};
      }
    }

    // Handle non-tuple types using existing converter
    return AbiTypeConverter.convertType(solidityType, indexed);
  }

  /** Create AbiEventInput with recursive tuple component processing */
  private AbiEventInput createAbiEventInput(AbiDefinition.NamedType input) {
    String name = input.getName();
    String type = input.getType();
    boolean indexed = input.isIndexed();

    // Handle tuple types by recursively processing components
    if (type.startsWith("tuple")) {
      List<AbiEventInput> components = extractTupleComponents(input);
      return new AbiEventInput(name, type, indexed, components);
    } else {
      // Non-tuple types
      return new AbiEventInput(name, type, indexed);
    }
  }

  /**
   * Extract tuple components recursively, similar to initInputParameter reference implementation
   */
  private List<AbiEventInput> extractTupleComponents(AbiDefinition.NamedType input) {
    try {
      // Try to get components from the input using reflection (similar to createTupleTypeReference)
      List<AbiDefinition.NamedType> components = null;
      try {
        java.lang.reflect.Method getComponentsMethod = input.getClass().getMethod("getComponents");
        components = (List<AbiDefinition.NamedType>) getComponentsMethod.invoke(input);
      } catch (NoSuchMethodException
          | IllegalAccessException
          | java.lang.reflect.InvocationTargetException e) {
        log.debug("getComponents method not available or failed for tuple: {}", input.getType());
        return Collections.emptyList();
      }

      if (components == null || components.isEmpty()) {
        log.debug("Tuple type {} has no components", input.getType());
        return Collections.emptyList();
      }

      // Recursively process each component (following initInputParameter pattern)
      List<AbiEventInput> componentInputs = new ArrayList<>();
      for (AbiDefinition.NamedType component : components) {
        AbiEventInput componentInput = createAbiEventInput(component);
        componentInputs.add(componentInput);
      }

      log.debug(
          "Extracted {} components from tuple type {}", componentInputs.size(), input.getType());
      return componentInputs;

    } catch (Exception e) {
      log.warn(
          "Failed to extract components from tuple type {}: {}", input.getType(), e.getMessage());
      return Collections.emptyList();
    }
  }

  /**
   * Parse ABI content from an input stream and register contracts/events
   *
   * @param inputStream The input stream containing ABI JSON
   * @param objectKey The S3 object key
   * @param lastModified Last modified timestamp
   * @return Contract information including address and name
   * @throws IOException If parsing fails
   */
  public ContractInfo parseAbiContent(InputStream inputStream, String objectKey, Date lastModified)
      throws IOException {
    try {
      byte[] abiJson = inputStream.readAllBytes();

      // Extract contract name from object key
      String[] pathParts = objectKey.split("/");
      String contractName = pathParts[1].replace(".json", "");

      // Extract address from JSON based on ABI format
      String abiFormat = properties.getAbiFormat();
      String address;

      JsonNode rootNode = mapper.readTree(abiJson);
      if ("truffle".equals(abiFormat)) {
        // For Truffle format, find address in networks section
        JsonNode networksNode = rootNode.path("networks");
        address = findFirstAddressInNetworks(networksNode);
      } else {
        // For other formats (like Hardhat), get address directly
        address = rootNode.path("address").asText();
      }

      address = address.toLowerCase();

      // Parse ABI section
      JsonNode abiNode = rootNode.path("abi");
      if (abiNode.isMissingNode()) {
        String errorMessage = "ABI section not found in JSON";
        log.error(errorMessage);
        throw new IOException(errorMessage);
      }

      // append the contract address
      appendContractAddress(address);

      // parse and register events
      parseAndRegisterEvents(address, contractName, abiNode.toString());

      log.info(
          "ABI file processed: address={}, contract_name={}, last_modified={}, events={}",
          address,
          contractName,
          lastModified,
          contractEventStore.size());

      return ContractInfo.builder()
          .address(address)
          .name(contractName)
          .lastModified(lastModified)
          .build();
    } finally {
      inputStream.close();
    }
  }

  /**
   * Find the first address in the networks section of the ABI JSON
   *
   * @param networksNode The networks node from the ABI JSON
   * @return The first address found, or an empty string if none found
   */
  private String findFirstAddressInNetworks(JsonNode networksNode) {
    if (networksNode.isObject()) {
      Iterator<JsonNode> elements = networksNode.elements();
      while (elements.hasNext()) {
        JsonNode network = elements.next();
        if (network.has(DCFConst.ADDRESS)) {
          return network.get(DCFConst.ADDRESS).asText();
        }
      }
    }
    return "";
  }

  /**
   * Adds a contract address to the list of monitored addresses
   *
   * @param address The contract address to add
   */
  public void appendContractAddress(String address) {
    if (!contractAddresses.contains(address)) {
      contractAddresses.add(address);
      log.info("Added contract address: {}", address);
    }
  }

  /**
   * Parse ABI JSON and register events for a contract
   *
   * @param address Contract address
   * @param contractName Contract name
   * @param abiJson ABI JSON string
   * @throws IOException If parsing fails
   */
  public void parseAndRegisterEvents(String address, String contractName, String abiJson)
      throws IOException {
    contractEventStore.put(
        address,
        ContractEvents.builder().contractName(contractName).events(parseAbi(abiJson)).build());
    log.info("Registered events for contract: {} at address: {}", contractName, address);
  }

  /**
   * Retrieves the ABI event definition for a given log.
   *
   * @param ethLog The Ethereum log to process
   * @return The ABI event definition for the log
   * @throws Exception If no matching event is found
   */
  public org.web3j.abi.datatypes.Event getABIEventByLog(Log ethLog) throws Exception {
    String eventId = ethLog.getTopics().get(0).toLowerCase();
    String logAddress = ethLog.getAddress().toLowerCase();

    log.info("Looking for event with signature: {} for address: {}", eventId, logAddress);

    Map<String, ContractAbiEvent> events =
        contractEventStore.containsKey(logAddress)
            ? contractEventStore.get(logAddress).events
            : Collections.emptyMap();

    log.info("Available signatures for address {}: {}", logAddress, events.keySet());

    if (events.containsKey(eventId)) {
      log.info("Found matching event for signature: {}", eventId);
      return events.get(eventId).getEvent();
    }

    log.warn("No matching event found for signature: {} at address: {}", eventId, logAddress);
    return null;
  }

  /**
   * Retrieves the contract ABI event definition for a given log.
   *
   * @param log The Ethereum log to process
   * @return The contract ABI event definition for the log
   * @throws Exception If no matching event is found
   */
  public ContractAbiEvent getContractAbiEventByLog(Log log) throws Exception {
    String eventId = log.getTopics().get(0).toLowerCase();
    String logAddress = log.getAddress().toLowerCase();

    Map<String, ContractAbiEvent> events =
        contractEventStore.containsKey(logAddress)
            ? contractEventStore.get(logAddress).events
            : Collections.emptyMap();
    if (events.containsKey(eventId)) {
      return events.get(eventId);
    }

    return null;
  }
}
